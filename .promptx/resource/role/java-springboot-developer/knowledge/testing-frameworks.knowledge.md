<knowledge>
  ## 测试框架知识体系
  
  ### 1. 单元测试 (Unit Testing)
  - **JUnit 5**: Jupiter, Vintage, Platform模块、`@Test`、`@BeforeEach`、`@AfterEach`、`@BeforeAll`、`@AfterAll`、`@DisplayName`、参数化测试、动态测试
  - **Mockito**: Mocking框架、`@Mock`、`@InjectMocks`、`when().thenReturn()`、`verify()`、参数匹配器
  - **AssertJ**: 流式断言库，提高断言可读性
  - **Spring Boot Test**: `@SpringBootTest`、`@WebMvcTest`、`@DataJpaTest`、`@MockBean`、`TestRestTemplate`、`WebTestClient`
  
  ### 2. 集成测试 (Integration Testing)
  - **Spring Boot Test**: 使用`@SpringBootTest`加载完整的Spring上下文进行集成测试
  - **Testcontainers**: 在Docker容器中运行真实的服务（如数据库、消息队列）进行集成测试
  - **REST Assured**: 用于RESTful API的集成测试，支持链式调用和DSL
  
  ### 3. 端到端测试 (End-to-End Testing)
  - **Selenium/Playwright**: 浏览器自动化测试工具，用于Web UI测试
  - **Cucumber/JBehave**: BDD (行为驱动开发) 框架，使用Gherkin语言编写可读性强的测试场景
  
  ### 4. 性能测试 (Performance Testing)
  - **JMeter**: 负载测试工具，模拟大量用户请求，分析系统性能
  - **Gatling**: 基于Scala的开源性能测试工具，支持DSL编写测试脚本
  
  ### 5. 契约测试 (Contract Testing)
  - **Pact**: 消费者驱动的契约测试框架，确保服务提供者和消费者之间的兼容性
  
  ### 6. 静态代码分析与代码质量
  - **SonarQube**: 静态代码分析平台，检测代码异味、漏洞、bug，管理技术债务
  - **Checkstyle**: 检查Java代码是否符合编码规范
  - **PMD**: 查找潜在的bug、死代码、重复代码等
  - **FindBugs/SpotBugs**: 查找Java代码中的缺陷
  
  ### 7. 测试策略与实践
  - **测试金字塔**: 单元测试、集成测试、端到端测试的比例和优先级
  - **TDD (Test-Driven Development)**: 测试驱动开发流程
  - **BDD (Behavior-Driven Development)**: 行为驱动开发流程
  - **CI/CD中的测试**: 在持续集成/持续部署管道中自动化测试
  - **测试数据管理**: 测试数据的准备、清理和维护
  - **测试报告与度量**: 测试覆盖率、缺陷密度、测试通过率等
</knowledge> 