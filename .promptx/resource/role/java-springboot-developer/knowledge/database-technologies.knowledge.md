<knowledge>
  ## 数据库技术知识体系
  
  ### 1. 关系型数据库 (RDBMS)
  - **MySQL**: 存储引擎（InnoDB, MyISAM）、索引优化、事务隔离级别、锁机制、主从复制、分库分表
  - **PostgreSQL**: 特性、数据类型、索引、高级功能（JSONB, CTE, 窗口函数）
  - **SQL语言**: DDL, DML, DCL、连接查询、子查询、聚合函数、事务控制
  - **数据库设计**: ER模型、范式理论（1NF, 2NF, 3NF, BCNF）、反范式
  - **JDBC**: Java数据库连接、连接池（HikariCP, Druid）
  - **ORM框架**: JPA/Hibernate（实体映射、关联关系、缓存）、MyBatis（XML配置、Mapper接口、动态SQL）
  
  ### 2. NoSQL 数据库
  - **Redis**: 键值存储、数据结构（String, Hash, List, Set, ZSet）、持久化（RDB, AOF）、集群模式、缓存雪崩/穿透/击穿
  - **MongoDB**: 文档型数据库、BSON、索引、副本集、分片、聚合管道
  - **Elasticsearch**: 分布式搜索和分析引擎、倒排索引、文档、索引、类型、映射、查询语言（DSL）
  - **Cassandra**: 列式数据库、分区键、集群架构、高可用性
  
  ### 3. 消息队列 (MQ)
  - **Kafka**: 分布式流平台、Producer, Consumer, Broker, Topic, Partition、高吞吐量、持久性、Replication
  - **RabbitMQ**: AMQP协议、Exchange (Direct, Fanout, Topic, Headers)、Queue, Binding、消息确认机制
  - **RocketMQ**: 阿里巴巴开源分布式消息中间件、顺序消息、事务消息、定时消息、消息过滤
  - **消息队列的选型**: 吞吐量、延迟、消息可靠性、一致性、生态系统
  
  ### 4. 数据库优化与管理
  - **索引优化**: 复合索引、覆盖索引、前缀索引、索引失效场景
  - **SQL优化**: 避免全表扫描、优化JOIN操作、使用EXPLAIN分析执行计划
  - **读写分离**: 提高数据库并发能力
  - **分库分表**: 应对大数据量和高并发挑战
  - **数据库连接池优化**: 合理设置连接数、超时时间
  - **备份与恢复**: 定期备份策略、灾难恢复计划
  - **数据库监控**: 监控数据库性能指标、连接数、慢查询
</knowledge> 