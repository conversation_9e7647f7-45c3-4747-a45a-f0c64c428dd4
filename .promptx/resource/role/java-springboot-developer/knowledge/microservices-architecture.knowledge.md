<knowledge>
  ## 微服务架构知识体系
  
  ### 1. 微服务核心概念
  - **单体应用与微服务**: 优缺点对比、演进路径
  - **服务拆分原则**: 业务能力边界、康威定律、DDD (领域驱动设计)
  - **独立部署**: 每个微服务独立打包、部署和运行
  - **去中心化治理**: 每个服务独立选择技术栈、独立数据存储
  - **弹性与容错**: 服务熔断、降级、限流、重试
  
  ### 2. 微服务通信
  - **同步通信**: RESTful API、gRPC、HTTP/2
  - **异步通信**: 消息队列 (Kafka, RabbitMQ, RocketMQ)、事件驱动架构
  - **API Gateway**: API网关的作用（路由、认证、授权、限流、缓存）
  - **服务注册与发现**: Eureka、Consul、Nacos、Zookeeper
  
  ### 3. 微服务数据管理
  - **数据一致性**: 分布式事务 (2PC, TCC, Saga)、最终一致性
  - **数据库拆分**: 垂直拆分、水平拆分、读写分离
  - **数据同步**: ETL、消息队列、Change Data Capture (CDC)
  
  ### 4. 微服务治理与运维
  - **配置中心**: 集中管理配置，动态刷新，如Spring Cloud Config、Nacos Config
  - **链路追踪**: Zipkin、Sleuth、SkyWalking，实现请求在服务间的追踪
  - **日志管理**: ELK Stack (Elasticsearch, Logstash, Kibana)、Grafana Loki
  - **监控与告警**: Prometheus、Grafana，监控服务健康、性能指标
  - **自动化部署**: Docker、Kubernetes、Jenkins、GitLab CI/CD
  - **灰度发布与蓝绿部署**: 降低发布风险
  
  ### 5. 微服务安全
  - **认证与授权**: OAuth2、JWT、Spring Security OAuth2
  - **API安全**: API密钥、HTTPS、输入验证
  - **数据加密**: 传输加密、存储加密
  
  ### 6. 微服务最佳实践
  - **高可用设计**: 多活架构、异地多活、灾备
  - **容错设计**: 熔断、降级、隔离、限流
  - **限流**: 令牌桶、漏桶算法、Sentinel
  - **服务网格**: Istio、Linkerd，统一流量管理、安全、可观测性
  - **领域驱动设计 (DDD)**: 战术设计（聚合、实体、值对象、领域服务、仓储）、战略设计（限界上下文、上下文映射）
</knowledge> 