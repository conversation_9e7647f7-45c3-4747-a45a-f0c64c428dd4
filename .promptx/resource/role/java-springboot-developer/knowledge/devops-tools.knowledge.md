<knowledge>
  ## DevOps 工具知识体系
  
  ### 1. 版本控制
  - **Git**: 分布式版本控制系统、基本命令（clone, pull, push, commit, branch, merge, rebase）、分支策略（Git Flow, GitHub Flow, GitLab Flow）
  - **GitHub/GitLab/Bitbucket**: 代码托管平台、Pull Request/Merge Request、Issue跟踪、CI/CD集成
  
  ### 2. 持续集成/持续部署 (CI/CD)
  - **Jenkins**: 老牌CI/CD工具、Pipeline as Code (Jenkinsfile)、插件生态、分布式构建
  - **GitLab CI/CD**: GitLab内置CI/CD、`.gitlab-ci.yml`配置、Runner
  - **GitHub Actions**: GitHub内置CI/CD、Workflow YAML配置、Actions市场
  - **Maven/Gradle**: 构建工具、生命周期、插件、依赖管理
  - **SonarQube**: 静态代码质量管理平台、代码扫描、技术债务分析
  
  ### 3. 容器化技术
  - **Docker**: 容器化技术、Dockerfile、镜像、容器、卷、网络、Docker Compose
  - **Kubernetes (K8s)**: 容器编排平台、Pod, Deployment, Service, Ingress, Volume、部署策略、Helm
  - **Harbor**: 容器镜像仓库管理、镜像安全扫描、权限管理
  
  ### 4. 自动化运维与配置管理
  - **Ansible**: 自动化配置管理、部署、编排工具、YAML Playbook、Ad-hoc命令
  - **Terraform**: 基础设施即代码 (IaC)、多云支持、HCL语言、State管理
  - **Chef/Puppet**: 配置管理工具
  
  ### 5. 监控与日志
  - **Prometheus**: 开源监控系统、时序数据库、Pull模式、Alertmanager
  - **Grafana**: 数据可视化工具、Metrics dashboard
  - **ELK Stack**: Elasticsearch (数据存储和搜索), Logstash (日志收集和处理), Kibana (日志可视化)
  - **SkyWalking/Zipkin/Sleuth**: 分布式链路追踪系统
  - **Loki**: 基于标签的日志聚合系统
  
  ### 6. 云平台服务
  - **AWS/Azure/GCP**: 云计算平台的核心服务（EC2/VM, S3/Blob Storage, RDS/Database, Lambda/Functions, Kubernetes Service）
  - **Serverless**: 无服务器架构、AWS Lambda, Azure Functions, Google Cloud Functions
  
  ### 7. 实践与理念
  - **DevOps文化**: 持续改进、自动化、跨职能协作
  - **SRE (Site Reliability Engineering)**: 站点可靠性工程、SLI/SLO/SLA、错误预算
  - **GitOps**: 使用Git作为单一事实来源进行基础设施和应用部署
</knowledge> 