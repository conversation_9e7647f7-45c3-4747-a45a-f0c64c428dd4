<knowledge>
  ## Java核心知识体系
  
  ### 1. Java基础
  - **JVM**: Java虚拟机原理、内存模型（堆、栈、方法区、程序计数器、本地方法栈）、类加载机制、垃圾回收（GC算法、GC收集器）
  - **JRE/JDK**: 区别与联系，Java开发环境配置
  - **数据类型**: 基本数据类型、引用数据类型、装箱与拆箱
  - **运算符与流程控制**: 各种运算符、if/else、switch、for/while循环、break/continue
  - **面向对象编程(OOP)**: 类、对象、封装、继承、多态、抽象类、接口、重载、重写
  - **常用类**: String、StringBuilder、StringBuffer、包装类、System、Math
  - **异常处理**: 异常分类、try-catch-finally、throws、自定义异常
  - **集合框架**: List (ArrayList, LinkedList, Vector)、Set (HashSet, TreeSet, LinkedHashSet)、Map (HashMap, TreeMap, LinkedHashMap, ConcurrentHashMap)、Queue
  - **IO**: 文件IO、字节流、字符流、缓冲流、转换流、序列化
  - **反射**: 反射机制原理、Class类、Constructor、Method、Field的使用
  - **注解**: 元注解、自定义注解、注解处理器
  
  ### 2. Java并发编程
  - **线程**: 线程的创建与启动（Thread、Runnable、Callable）、线程生命周期、线程同步（synchronized、Lock）、线程通信（wait/notify/notifyAll）
  - **JUC**: java.util.concurrent包、线程池（ThreadPoolExecutor、Executors）、并发集合、原子类（AtomicInteger）、Future
  - **并发模型**: CAS、AQS（AbstractQueuedSynchronizer）原理
  
  ### 3. Java 8+ 特性
  - **Lambda表达式**: 函数式接口、方法引用
  - **Stream API**: 集合的流式操作、并行流
  - **Optional**: 避免空指针异常
  - **日期时间API**: `java.time`包，替代`java.util.Date`和`Calendar`
  - **新集合API**: `forEach`、`removeIf`、`replaceAll`、`compute`等
  
  ### 4. 工具与构建
  - **Maven/Gradle**: 项目构建、依赖管理、生命周期、插件使用
  - **Git**: 版本控制基础操作、分支管理、冲突解决
  - **IDE**: IntelliJ IDEA、Eclipse等常用开发工具的使用技巧
  
  ### 5. 设计模式
  - **创建型**: 单例、工厂方法、抽象工厂、建造者、原型
  - **结构型**: 适配器、装饰器、代理、组合、外观、享元、桥接
  - **行为型**: 策略、模板方法、观察者、迭代器、责任链、命令、备忘录、状态、访问者、中介者、解释器
</knowledge> 