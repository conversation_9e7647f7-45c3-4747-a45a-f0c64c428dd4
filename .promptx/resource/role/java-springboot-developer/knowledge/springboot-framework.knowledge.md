<knowledge>
  ## Spring Boot 框架知识体系
  
  ### 1. Spring Boot 核心特性
  - **自动配置**: 理解自动配置原理、`@EnableAutoConfiguration`、`@Conditional`注解
  - **Starter POMs**: 简化依赖管理，提高开发效率
  - **外部化配置**: `application.properties`/`application.yml`、`@Value`、`Environment`、`SpringApplication.setDefaultProperties()`、命令行参数、JMX等
  - **Actuator**: 监控和管理生产环境，如健康检查、度量指标、HTTP追踪等
  - **嵌入式Web服务器**: Jetty、Tomcat、Undertow，打包即可运行
  - **Spring Initializr**: 快速创建Spring Boot项目脚手架
  
  ### 2. Spring 核心概念
  - **IoC (Inversion of Control)**: 控制反转、Bean的生命周期、Bean的作用域（singleton, prototype, request, session, application, websocket）
  - **DI (Dependency Injection)**: 依赖注入、`@Autowired`、`@Resource`、构造器注入、Setter注入
  - **AOP (Aspect-Oriented Programming)**: 面向切面编程、切面、连接点、切入点、通知（Before, After, AfterReturning, AfterThrowing, Around）
  - **Spring MVC**: DispatcherServlet、Controller、RequestMapping、ModelAndView、数据绑定、视图解析
  - **Spring Data**: JPA、MyBatis集成、Repository接口、CRUD操作
  - **Spring Security**: 认证、授权、加密、CSRF防护、OAuth2
  - **Spring Cloud**: 微服务开发套件概述（服务发现、配置中心、网关、熔断器、链路追踪等）
  
  ### 3. Spring Boot 实战
  - **RESTful API开发**: `@RestController`、`@RequestBody`、`@ResponseBody`、HTTP方法（GET, POST, PUT, DELETE）
  - **数据访问**: Spring Data JPA、Spring JDBC Template、MyBatis集成
  - **事务管理**: `@Transactional`、事务传播行为、隔离级别
  - **缓存**: Spring Cache抽象、集成Redis、Ehcache等
  - **消息队列集成**: RabbitMQ、Kafka、ActiveMQ的Spring Boot Starter集成
  - **异步编程**: `@Async`注解、CompletableFuture
  - **定时任务**: `@Scheduled`注解
  - **国际化**: MessageSource配置
  
  ### 4. 最佳实践与调优
  - **日志管理**: Logback、Log4j2配置，日志级别、Appender
  - **性能调优**: JVM参数调优、数据库连接池优化、缓存策略、并发优化
  - **安全实践**: 密码加密、SQL注入防范、XSS防范、HTTPS配置
  - **监控与告警**: Actuator配合Prometheus、Grafana，ELK Stack进行日志分析
  - **优雅停机**: 实现Spring Boot应用的优雅停机
</knowledge> 