<execution>
  <constraint>
    ## Java Spring Boot 开发工作流限制
    - **项目规范**：必须遵循公司或团队的编码规范、Git工作流、代码审查流程
    - **技术栈依赖**：受限于Java版本、Spring Boot版本、以及其他依赖库的兼容性
    - **环境差异**：开发、测试、生产环境配置差异需要妥善管理
    - **资源限制**：服务器性能、数据库连接、网络带宽等硬件或服务资源限制
  </constraint>

  <rule>
    ## Java Spring Boot 开发工作流强制规则
    - **分支管理**：新功能开发必须从`develop`或`main`分支创建Feature分支
    - **代码提交**：每次提交必须包含清晰的提交信息，遵循约定式提交规范
    - **代码审查**：所有代码必须经过至少一名同事的代码审查后才能合并
    - **单元测试覆盖率**：核心业务逻辑必须有单元测试，并达到设定覆盖率标准
    - **安全编码实践**：必须遵循OWASP TOP 10等安全编码准则，防止常见漏洞
    - **异常处理**：所有潜在的运行时异常必须被妥善捕获和处理
  </rule>

  <guideline>
    ## Java Spring Boot 开发工作流指导原则
    - **模块化设计**：高内聚、低耦合，每个服务或模块职责单一
    - **TDD/BDD**：鼓励使用测试驱动开发或行为驱动开发，先写测试再实现功能
    - **持续集成**：及时将代码集成到主干，减少集成问题
    - **文档先行**：重要的设计、API接口、复杂逻辑需提前编写文档或注释
    - **日志规范**：统一日志格式和级别，便于问题排查和监控
    - **配置外部化**：敏感信息和环境相关配置应外部化管理，不硬编码
  </guideline>

  <process>
    ## Java Spring Boot 标准开发流程
    
    ### Step 1: 需求分析与设计
    ```mermaid
    flowchart TD
        A[接收需求] --> B(需求评审)
        B --> C{技术方案设计}
        C --> D[数据库设计]
        C --> E[API接口设计]
        D & E --> F[评审与确认]
    ```
    
    ### Step 2: 编码实现
    ```mermaid
    flowchart TD
        A[创建新分支] --> B[编写核心业务逻辑]
        B --> C[实现Controller层]
        C --> D[实现Service层]
        D --> E[实现Repository层]
        E --> F[单元测试编写]
        F --> G[本地集成测试]
    ```
    
    ### Step 3: 测试与集成
    ```mermaid
    flowchart TD
        A[提交代码] --> B[创建Pull Request]
        B --> C(代码审查)
        C --> D{CI/CD自动化测试}
        D -->|通过| E[合并到主干]
        D -->|失败| A
        E --> F[部署到测试环境]
    ```
    
    ### Step 4: 部署与运维
    ```mermaid
    flowchart TD
        A[测试环境验证] --> B[部署到生产环境]
        B --> C[持续监控]
        C --> D[性能优化与迭代]
        D --> End([完成])
    ```
  </process>

  <criteria>
    ## Java Spring Boot 开发质量标准
    
    ### 代码质量
    - ✅ 代码可读性高，遵循编码规范
    - ✅ 模块职责单一，高内聚低耦合
    - ✅ 错误处理机制完善，鲁棒性强
    - ✅ 注释清晰，关键逻辑有文档说明
    
    ### 测试质量
    - ✅ 单元测试覆盖核心业务逻辑
    - ✅ 集成测试验证模块间交互
    - ✅ 测试用例覆盖边界条件和异常场景
    
    ### 性能与稳定性
    - ✅ API响应时间符合预期
    - ✅ 资源利用率（CPU、内存）合理
    - ✅ 系统在高并发下稳定运行
    - ✅ 日志和监控系统完善，便于问题排查
    
    ### 安全性
    - ✅ 防止常见的Web漏洞，如SQL注入、XSS、CSRF
    - ✅ 认证授权机制健全
    - ✅ 数据加密和隐私保护措施到位
  </criteria>
</execution> 