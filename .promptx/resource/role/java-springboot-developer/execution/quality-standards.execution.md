<execution>
  <constraint>
    ## 质量标准限制
    - **项目范围**：质量标准需与项目规模、复杂度和风险等级相匹配
    - **团队能力**：团队成员的技术水平和经验影响标准的实施和达成
    - **时间/资源**：严格的质量标准可能增加开发时间和资源投入
    - **工具限制**：依赖静态代码分析工具、测试框架等，其功能限制会影响标准执行
  </constraint>

  <rule>
    ## 质量标准强制规则
    - **可读性**：代码必须清晰可读，遵循统一的编码规范（如Google Java Style Guide、阿里巴巴Java开发手册）
    - **可维护性**：模块职责单一，高内聚低耦合，避免过度设计和复杂性
    - **测试覆盖率**：单元测试覆盖率达到特定阈值（例如：核心业务逻辑80%以上）
    - **错误处理**：所有潜在异常必须被妥善处理，避免系统崩溃或数据丢失
    - **安全性**：遵循OWASP TOP 10等安全编码实践，定期进行安全审计
    - **代码审查**：所有代码在合并前必须经过至少一次严格的代码审查
  </rule>

  <guideline>
    ## 质量标准指导原则
    - **性能优化**：关注代码性能，避免不必要的资源消耗，对关键路径进行性能测试
    - **可扩展性**：设计时考虑未来功能扩展，避免硬编码和紧密耦合
    - **文档规范**：关键设计、复杂逻辑、公共API需有清晰的文档或注释
    - **日志与监控**：设计完善的日志系统，提供必要的监控指标，便于问题排查和性能分析
    - **灰度发布**：支持小范围灰度发布，降低上线风险
    - **回滚能力**：确保系统具备快速回滚到前一版本的能力
  </guideline>

  <process>
    ## 质量保证流程
    
    ### Step 1: 需求分析阶段
    ```mermaid
    flowchart TD
        A[明确非功能性需求] --> B[定义质量目标和指标]
        B --> C[风险评估与规避策略]
    ```
    
    ### Step 2: 设计阶段
    ```mermaid
    flowchart TD
        A[架构设计评审] --> B[模块设计评审]
        B --> C[接口规范制定]
        C --> D[技术选型合理性评估]
    ```
    
    ### Step 3: 编码阶段
    ```mermaid
    flowchart TD
        A[编码规范检查] --> B[静态代码分析（SonarQube等）]
        B --> C[单元测试编写与执行]
        C --> D[代码审查]
    ```
    
    ### Step 4: 测试阶段
    ```mermaid
    flowchart TD
        A[集成测试] --> B[系统测试]
        B --> C[性能测试]
        C --> D[安全测试]
        D --> E[用户验收测试 (UAT)]
    ```
    
    ### Step 5: 部署与运维阶段
    ```mermaid
    flowchart TD
        A[CI/CD流程自动化] --> B[自动化部署]
        B --> C[持续监控与告警]
        C --> D[生产环境故障排查与修复]
        D --> E[定期性能优化与架构迭代]
    ```
  </process>

  <criteria>
    ## 质量标准评估指标
    
    ### 功能性指标
    - ✅ 功能完整性：需求覆盖率100%
    - ✅ 功能正确性：缺陷密度低于0.1个/千行代码
    
    ### 性能指标
    - ✅ 响应时间：API平均响应时间低于100ms
    - ✅ 吞吐量：每秒处理请求数达到设计峰值
    - ✅ 资源利用率：CPU、内存使用率在健康范围内
    
    ### 可靠性指标
    - ✅ 平均故障间隔时间 (MTBF)：大于30天
    - ✅ 平均恢复时间 (MTTR)：小于10分钟
    - ✅ 错误率：接口错误率低于0.1%
    
    ### 可维护性指标
    - ✅ 代码圈复杂度：函数圈复杂度小于10
    - ✅ 代码重复率：低于5%
    - ✅ 变更影响范围：单个变更影响模块数小于3个
    
    ### 安全性指标
    - ✅ 漏洞密度：OWASP TOP 10漏洞零发现
    - ✅ 安全审计通过率：100%
    
    ### 可用性指标
    - ✅ 服务可用性：99.99%以上
    - ✅ 备份与恢复：数据备份策略健全，恢复流程可操作
  </criteria>
</execution> 