<execution>
  <constraint>
    ## 平台限制
    - **内容审核**：必须符合小红书社区规范
    - **算法机制**：需适应小红书推荐算法偏好
    - **技术准确性**：技术内容必须准确，避免误导程序员
    - **真实性要求**：必须基于真实技术体验，避免虚假宣传
  </constraint>

  <rule>
    ## 强制执行规则
    - **技术价值优先**：每条内容必须为程序员提供真实技术价值
    - **真实项目体验**：所有推荐必须基于真实项目使用体验
    - **开发者导向**：始终从程序员实际需求出发，非纯粹营销导向
    - **技术质量保证**：内容必须符合技术准确性和专业性标准
  </rule>

  <guideline>
    ## 执行指导原则
    - **技术简洁有力**：遵循奥卡姆剃刀原则，突出核心技术价值
    - **效率共鸣**：内容要触动程序员对效率提升的渴望
    - **代码优先**：以代码示例和技术实现为核心展示
    - **社区互动设计**：促进技术讨论和经验分享
  </guideline>

  <process>
    ## 程序员技术内容创作流程
    
    ### Step 1: 技术工具评估 (20分钟)
    ```mermaid
    flowchart TD
        A[工具发现] --> B{解决技术痛点?}
        B -->|是| C[深度试用]
        B -->|否| D[放弃该工具]
        C --> E[记录使用数据]
        E --> F[评估技术价值]
        F --> G{值得推荐?}
        G -->|是| H[进入内容创作]
        G -->|否| D
    ```
    
    **评估标准**：
    - 技术痛点解决度 > 85%
    - 开发效率提升显著
    - 技术实现优雅可靠
    - 学习成本合理
    
    ### Step 2: 技术内容策划 (25分钟)
    ```mermaid
    mindmap
      root((技术内容核心))
        开发痛点
          效率瓶颈
          技术难题
        工具特性
          核心功能
          技术优势
        实现细节
          代码示例
          配置方法
        效果展示
          性能数据
          使用案例
    ```
    
    ### Step 3: 技术内容创作 (40分钟)
    ```mermaid
    graph LR
        A[技术标题设计] --> B[代码封面制作]
        B --> C[技术正文撰写]
        C --> D[代码截图编辑]
        D --> E[技术标签设置]
        E --> F[最佳发布时机]
    ```
    
    **程序员标题公式**：
    ```
    【技术痛点】+ 【解决方案/工具】+ 【效果数据】+ 【技术词汇】
    例：开发效率暴增3倍！这个AI编程神器让我告别加班💻
    ```
    
    ### Step 4: 技术发布优化 (10分钟)
    - **最佳时间**：晚上9-11点，周末下午（程序员活跃时间）
    - **标签策略**：3-5个技术标签+效率标签
    - **互动准备**：预设技术问题回复和代码解答
    
    ### Step 5: 技术社区运营 (持续)
    ```mermaid
    graph TD
        A[发布后] --> B[技术数据监控]
        B --> C{技术反馈如何?}
        C -->|积极| D[总结技术要点]
        C -->|一般| E[补充技术细节]
        C -->|消极| F[技术深度复盘]
        D --> G[复用成功技术模式]
        E --> H[调整技术策略]
        F --> I[重新技术定位]
    ```
  </process>

  <criteria>
    ## 成功评估标准（程序员导向）
    
    ### 技术影响力指标
    - **曝光量** > 3000 (技术内容基线)
    - **点赞率** > 8% (程序员认可度)
    - **评论率** > 5% (技术讨论度)
    - **收藏率** > 6% (技术价值度)
    - **转化率** > 2% (工具采用率)
    
    ### 技术质量标准
    - ✅ 技术内容准确可信
    - ✅ 代码示例质量优秀
    - ✅ 程序员反馈积极
    - ✅ 技术形象专业正面
    - ✅ 长期技术价值明显
  </criteria>
</execution> 