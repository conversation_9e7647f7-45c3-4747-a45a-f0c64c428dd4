<execution>
  <constraint>
    ## 童话结构的客观限制
    - **经典模式传承**：必须遵循童话的基本叙事传统和读者期待
    - **篇幅控制**：童话通常需要在2000-5000字内完成完整叙述
    - **年龄适应性**：结构复杂度必须适合儿童的认知发展水平
    - **文化普适性**：结构设计需要跨越文化差异，具有普遍理解性
    - **插图配合**：结构节点需要考虑与插图的配合效果
  </constraint>

  <rule>
    ## 童话结构的强制规则
    - **三幕式结构**：必须包含建立-发展-解决三个基本阶段
    - **主角中心**：故事必须围绕主角的成长或变化展开
    - **冲突驱动**：每个结构段落都必须有明确的冲突或张力
    - **因果关系**：情节发展必须有清晰的因果逻辑链条
    - **情感弧线**：主角必须经历完整的情感变化过程
    - **圆满收束**：结局必须给予希望和温暖，符合童话特质
  </rule>

  <guideline>
    ## 童话结构的指导原则
    - **简洁明了**：结构层次清晰，避免过度复杂的嵌套情节
    - **节奏感强**：紧张与舒缓交替，保持读者的注意力
    - **象征运用**：通过结构安排强化故事的象征意义
    - **情感共鸣**：结构设计服务于情感表达和价值传递
    - **经典创新**：在传统结构基础上适度创新
    - **视觉化考虑**：结构节点便于转化为视觉表现
  </guideline>

  <process>
    ## 童话结构设计的系统流程

    ### Phase 1: 结构类型选择 (15分钟)

    #### 1.1 经典童话结构模式
    ```
    A. 英雄历险模式 (Hero's Journey)
    适用：成长主题、冒险故事
    结构：平凡世界 → 冒险召唤 → 拒绝召唤 → 遇见导师 → 
          跨越门槛 → 试炼考验 → 启示时刻 → 最终考验 → 
          获得奖赏 → 踏上归途 → 复活重生 → 带着灵药回归

    B. 灰姑娘模式 (Cinderella Pattern)
    适用：逆境成长、梦想实现
    结构：不幸处境 → 神奇帮助 → 短暂幸福 → 危机考验 → 
          真相显露 → 获得认可 → 幸福结局

    C. 三次重复模式 (Rule of Three)
    适用：智慧成长、技能学习
    结构：第一次尝试(失败) → 第二次尝试(部分成功) → 
          第三次尝试(完全成功) → 智慧获得

    D. 变形记模式 (Transformation)
    适用：内在成长、品格改变
    结构：初始状态 → 变化契机 → 适应过程 → 
          身份认知 → 真我发现 → 新生状态

    E. 寻宝模式 (Quest Pattern)
    适用：目标追求、价值发现
    结构：任务接受 → 准备出发 → 路途考验 → 
          宝藏发现 → 意义领悟 → 满载而归
    ```

    #### 1.2 结构选择决策树
    ```
    故事主题分析
    ├── 成长蜕变主题
    │   ├── 外在冒险 → 英雄历险模式
    │   ├── 内在改变 → 变形记模式
    │   └── 逆境突破 → 灰姑娘模式
    ├── 智慧学习主题
    │   ├── 技能掌握 → 三次重复模式
    │   └── 真理探寻 → 寻宝模式
    └── 情感关系主题
        ├── 友谊建立 → 英雄历险模式
        └── 爱的理解 → 变形记模式
    ```

    ### Phase 2: 结构框架设计 (30分钟)

    #### 2.1 三幕结构细化
    ```
    第一幕：建立阶段 (25% 篇幅)
    
    1.1 开场建立 (5%)
    - 主角介绍：基本信息和初始状态
    - 环境设定：故事发生的时空背景
    - 基调确立：故事的整体氛围和风格
    
    1.2 日常世界 (10%)
    - 正常生活：主角的日常状态和环境
    - 性格展现：通过行为展示主角特点
    - 问题暗示：隐含的不满足或缺失
    
    1.3 触发事件 (10%)
    - 平衡打破：改变现状的关键事件
    - 冲突引入：主要矛盾的初步显现
    - 目标确立：主角的愿望或使命明确
    ```

    ```
    第二幕：发展阶段 (50% 篇幅)
    
    2.1 冒险开始 (15%)
    - 踏出舒适圈：离开熟悉的环境
    - 新世界规则：了解新环境的运行法则
    - 盟友与敌人：重要配角的登场
    
    2.2 上升行动 (20%)
    - 小试牛刀：初步的挑战和成功
    - 技能学习：获得新的能力或知识
    - 关系发展：与其他角色的互动深化
    
    2.3 中点转折 (5%)
    - 重大发现：改变故事走向的关键信息
    - 策略调整：应对新情况的计划改变
    - 内心变化：主角心理状态的重要转变
    
    2.4 危机升级 (10%)
    - 困难加剧：面临更大的挑战
    - 信心动摇：对目标或能力的怀疑
    - 关系考验：重要关系面临考验
    ```

    ```
    第三幕：解决阶段 (25% 篇幅)
    
    3.1 最黑暗时刻 (5%)
    - 最大危机：面临最严峻的考验
    - 绝望边缘：似乎无法克服的困难
    - 内心挣扎：价值观和选择的冲突
    
    3.2 顿悟与行动 (10%)
    - 智慧获得：找到解决问题的关键
    - 勇气爆发：克服恐惧，采取行动
    - 能力展现：运用所学解决问题
    
    3.3 结局收束 (10%)
    - 冲突解决：主要矛盾得到化解
    - 成长展现：主角的变化和收获
    - 新平衡：建立更好的新状态
    - 温暖结尾：给予希望和美好
    ```

    #### 2.2 情节节点设计
    ```
    关键情节节点规划：
    
    节点1：开场钩子 (前100字)
    - 功能：立即抓住读者注意力
    - 方法：有趣情境、悬念设置、角色特色
    
    节点2：触发事件 (25%处)
    - 功能：推动故事正式开始
    - 方法：意外事件、重要发现、外力介入
    
    节点3：第一个转折 (37.5%处)
    - 功能：增加故事复杂性
    - 方法：新信息、计划改变、关系变化
    
    节点4：中点高潮 (50%处)
    - 功能：故事的重要转折点
    - 方法：重大发现、身份揭示、力量获得
    
    节点5：第二个转折 (62.5%处)
    - 功能：推向最终冲突
    - 方法：背叛、失败、新威胁
    
    节点6：最黑暗时刻 (75%处)
    - 功能：最大的危机和考验
    - 方法：绝境、选择、牺牲
    
    节点7：最终对决 (87.5%处)
    - 功能：运用所学解决问题
    - 方法：智慧、勇气、团结
    
    节点8：温暖结局 (100%处)
    - 功能：给予满足和希望
    - 方法：成长、团聚、新开始
    ```

    ### Phase 3: 角色弧线设计 (20分钟)

    #### 3.1 主角成长轨迹
    ```
    情感弧线设计：
    
    起点状态：
    - 外在状况：主角的客观处境
    - 内在状态：心理状态和情感需求
    - 核心缺失：需要获得或改变的东西
    
    变化过程：
    - 第一阶段：被动应对，依赖他人
    - 第二阶段：主动尝试，部分成功
    - 第三阶段：独立面对，完全成长
    
    终点状态：
    - 外在改变：客观处境的改善
    - 内在成长：心理和品格的提升
    - 价值实现：获得真正重要的东西
    ```

    #### 3.2 配角功能分配
    ```
    导师角色：
    - 出现时机：主角迷茫或需要指导时
    - 主要功能：提供智慧、工具或信心
    - 退场方式：主角独立后自然淡出
    
    伙伴角色：
    - 出现时机：冒险开始后
    - 主要功能：提供支持、友谊和帮助
    - 发展轨迹：与主角共同成长
    
    对手角色：
    - 出现时机：贯穿整个故事
    - 主要功能：制造冲突、考验主角
    - 结局处理：被感化、被击败或和解
    
    守护者角色：
    - 出现时机：关键门槛处
    - 主要功能：测试主角的决心和能力
    - 通过方式：证明资格或展现品格
    ```

    ### Phase 4: 结构优化调整 (15分钟)

    #### 4.1 节奏控制
    ```
    紧张度曲线：
    - 开场：中等紧张度，建立兴趣
    - 发展前期：逐步上升，保持参与
    - 发展中期：波浪起伏，避免疲劳
    - 发展后期：急剧上升，推向高潮
    - 解决阶段：快速下降，温暖收束
    
    节奏调节技巧：
    - 动静结合：行动场面与内心独白交替
    - 长短搭配：详细描写与简洁叙述结合
    - 张弛有度：紧张情节后安排舒缓段落
    ```

    #### 4.2 结构完整性检查
    ```
    逻辑检查清单：
    ☐ 每个情节点都有明确的因果关系
    ☐ 主角的行为动机清晰合理
    ☐ 配角的出现和退场都有必要性
    ☐ 冲突的设置和解决都符合逻辑
    ☐ 时间线清晰，没有矛盾之处
    ☐ 空间转换合理，便于理解
    
    情感检查清单：
    ☐ 主角的情感变化轨迹清晰
    ☐ 每个重要情节都有情感支撑
    ☐ 读者能够与主角产生共鸣
    ☐ 结局给人满足和温暖的感觉
    ☐ 整体情感基调统一协调
    ```
  </process>

  <criteria>
    ## 童话结构质量评价标准

    ### 结构完整性
    - ✅ 具有清晰的三幕式结构
    - ✅ 情节节点设置合理有效
    - ✅ 因果关系逻辑清晰
    - ✅ 时空转换自然流畅

    ### 角色弧线质量
    - ✅ 主角成长轨迹明确
    - ✅ 配角功能分配合理
    - ✅ 角色关系发展自然
    - ✅ 情感变化真实可信

    ### 节奏控制效果
    - ✅ 紧张度曲线设计合理
    - ✅ 动静结合，张弛有度
    - ✅ 保持读者持续兴趣
    - ✅ 高潮设置恰到好处

    ### 主题表达效果
    - ✅ 结构服务于主题表达
    - ✅ 价值观自然融入情节
    - ✅ 教育意义不生硬
    - ✅ 寓意深刻但易理解

    ### 创新与传承
    - ✅ 在经典基础上有所创新
    - ✅ 保持童话的基本特质
    - ✅ 适应现代读者需求
    - ✅ 具有独特的结构特色

    ### 实用性评估
    - ✅ 结构清晰便于执行
    - ✅ 适合目标篇幅要求
    - ✅ 便于插图配合
    - ✅ 易于改编为其他形式
  </criteria>
</execution> 