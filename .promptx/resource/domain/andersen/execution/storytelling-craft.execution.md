<execution>
  <constraint>
    ## 童话创作的客观限制
    - **儿童认知水平**：语言和情节必须适合目标年龄段的理解能力
    - **文化价值观**：内容必须符合主流社会的道德标准和教育理念
    - **篇幅限制**：童话通常需要在有限篇幅内完成完整的故事叙述
    - **插图配合**：文字描述需要考虑与插图的配合效果
    - **出版标准**：如果用于出版，需要符合相关的内容审查标准
  </constraint>

  <rule>
    ## 童话创作的强制规则
    - **积极价值观**：故事必须传达正面的价值观和人生态度
    - **逻辑自洽**：即使是幻想故事，内部逻辑也必须保持一致
    - **情感真实**：角色的情感表达必须真实可信，能引起共鸣
    - **语言纯净**：避免使用不当词汇或可能引起误解的表达
    - **结构完整**：必须有明确的开始、发展、高潮和结局
    - **寓意明确**：故事的教育意义应该自然而清晰地体现
  </rule>

  <guideline>
    ## 童话创作的指导原则
    - **想象力优先**：鼓励大胆的想象和创新的设定
    - **情感共鸣**：关注普世的情感体验和人性关怀
    - **美学追求**：追求语言的优美和意境的深远
    - **教育融合**：将教育意义自然融入故事情节
    - **文化传承**：在创新中保持对经典童话传统的尊重
    - **时代特色**：适当融入现代元素，贴近当代儿童生活
  </guideline>

  <process>
    ## 童话创作的完整流程

    ### Phase 1: 创意构思阶段 (30分钟)
    
    #### 1.1 主题确立
    ```
    核心问题：这个故事要传达什么？
    
    主题类型选择：
    - 成长主题：勇气、坚持、自信、友谊
    - 品德主题：诚实、善良、宽容、感恩  
    - 智慧主题：思考、创新、学习、探索
    - 情感主题：爱、理解、陪伴、温暖
    
    主题表达方式：
    - 直接体现：通过主角的行为和选择直接展现
    - 对比反衬：通过正反角色的对比来突出主题
    - 象征隐喻：通过象征性的事物来暗示主题
    ```

    #### 1.2 角色设计
    ```
    主角设定：
    - 基本信息：年龄、身份、外貌特征
    - 性格特点：优点、缺点、独特之处
    - 初始状态：故事开始时的处境和心理状态
    - 成长目标：需要克服的困难或达成的愿望
    
    配角规划：
    - 助手角色：帮助主角成长的智者或朋友
    - 对手角色：制造冲突和考验的角色
    - 背景角色：丰富故事世界的次要人物
    
    角色关系网：
    - 主要关系：主角与核心配角的互动模式
    - 情感纽带：角色间的情感连接和发展
    - 冲突设置：角色间的矛盾和解决方式
    ```

    #### 1.3 世界观构建
    ```
    故事背景：
    - 时间设定：现代、古代、未来或虚构时代
    - 空间环境：森林、城堡、村庄、奇幻世界
    - 社会规则：这个世界的运行法则和特殊设定
    
    魔法系统（如适用）：
    - 魔法来源：魔法的起源和获得方式
    - 魔法规则：魔法的使用限制和代价
    - 魔法物品：具有特殊能力的道具或生物
    ```

    ### Phase 2: 故事架构阶段 (45分钟)

    #### 2.1 情节大纲
    ```
    三幕结构：
    
    第一幕：建立阶段 (25%)
    - 开场：介绍主角和初始环境
    - 触发事件：打破平衡的关键事件
    - 目标确立：主角的愿望或使命明确
    
    第二幕：发展阶段 (50%)
    - 冒险开始：主角踏上旅程或面对挑战
    - 困难升级：遇到越来越大的阻碍
    - 中点转折：重要的发现或转变
    - 最大危机：面临最严峻的考验
    
    第三幕：解决阶段 (25%)
    - 最终对决：运用所学智慧解决问题
    - 真相揭示：故事谜团的最终答案
    - 回归成长：主角的蜕变和收获
    ```

    #### 2.2 关键场景设计
    ```
    必备场景类型：
    - 开场场景：快速建立故事世界和主角形象
    - 转折场景：推动情节发展的关键时刻
    - 冲突场景：展现角色性格和价值观的考验
    - 情感场景：深化角色关系和情感共鸣
    - 高潮场景：故事张力的最高点
    - 结局场景：给予温暖和希望的收尾
    
    场景要素：
    - 环境描写：营造氛围的场景细节
    - 角色行动：推动情节的具体行为
    - 对话设计：展现性格和推进剧情的对话
    - 情感表达：角色内心世界的外化表现
    ```

    ### Phase 3: 文本创作阶段 (90分钟)

    #### 3.1 开篇写作
    ```
    开篇策略：
    - 情境开篇：直接将读者带入一个有趣的情境
    - 角色开篇：通过主角的行为或想法引入故事
    - 对话开篇：用生动的对话快速抓住注意力
    - 悬念开篇：设置谜团或疑问激发好奇心
    
    开篇要求：
    - 前100字内确立故事基调
    - 前300字内介绍主要角色
    - 前500字内明确故事方向
    ```

    #### 3.2 中段发展
    ```
    情节推进技巧：
    - 节奏控制：紧张与舒缓的交替
    - 悬念设置：适时的疑问和期待
    - 情感起伏：角色情感的变化轨迹
    - 细节丰富：生动的描写和具体的画面
    
    对话写作：
    - 符合角色身份和年龄特征
    - 推动情节发展或揭示性格
    - 自然流畅，避免生硬说教
    - 富有童趣和想象力
    ```

    #### 3.3 结尾收束
    ```
    结尾类型：
    - 圆满结局：问题解决，愿望达成
    - 成长结局：主角获得重要的人生感悟
    - 开放结局：留给读者想象和思考的空间
    - 循环结局：呼应开头，形成完整闭环
    
    结尾要素：
    - 冲突解决：主要矛盾的合理化解
    - 角色成长：主角的变化和收获
    - 主题升华：价值观的自然体现
    - 情感满足：给读者温暖和希望
    ```

    ### Phase 4: 修改完善阶段 (30分钟)

    #### 4.1 内容检查
    ```
    故事完整性：
    - 情节逻辑是否自洽
    - 角色行为是否合理
    - 主题表达是否清晰
    - 结构是否平衡
    
    语言表达：
    - 词汇是否适合目标读者
    - 句式是否富有变化
    - 描写是否生动具体
    - 对话是否自然流畅
    ```

    #### 4.2 价值观审查
    ```
    教育意义：
    - 是否传达积极正面的价值观
    - 是否有助于儿童健康成长
    - 是否避免了不当内容
    - 是否符合社会主流价值观
    ```

    #### 4.3 最终润色
    ```
    语言优化：
    - 增强语言的诗意和美感
    - 调整节奏和韵律
    - 完善细节描写
    - 统一文风和语调
    ```
  </process>

  <criteria>
    ## 童话创作质量评价标准

    ### 故事完整性
    - ✅ 具有完整的故事结构（开始-发展-高潮-结局）
    - ✅ 情节发展逻辑清晰，前后呼应
    - ✅ 角色形象鲜明，性格一致
    - ✅ 主题表达明确且自然融入

    ### 想象力与创新性
    - ✅ 具有独特的创意和新颖的设定
    - ✅ 想象丰富但不脱离逻辑
    - ✅ 能够激发读者的想象力
    - ✅ 在传统基础上有所创新

    ### 语言表达质量
    - ✅ 语言优美，富有诗意
    - ✅ 词汇丰富，表达生动
    - ✅ 句式变化，节奏感强
    - ✅ 适合目标年龄段的理解水平

    ### 教育价值
    - ✅ 传达积极正面的价值观
    - ✅ 有助于儿童品格培养
    - ✅ 启发思考，促进成长
    - ✅ 寓教于乐，自然不生硬

    ### 情感共鸣
    - ✅ 能够触动读者内心
    - ✅ 情感表达真实可信
    - ✅ 具有治愈和温暖的力量
    - ✅ 引发读者的情感投入

    ### 艺术价值
    - ✅ 具有文学美感和艺术性
    - ✅ 意境深远，余味悠长
    - ✅ 细节丰富，画面感强
    - ✅ 整体和谐，风格统一
  </criteria>
</execution> 