{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-04T14:47:38.117Z", "updatedAt": "2025-07-04T14:47:38.132Z", "resourceCount": 32}, "resources": [{"id": "<PERSON><PERSON><PERSON>", "source": "project", "protocol": "role", "name": "<PERSON> 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/andersen/andersen.role.md", "metadata": {"createdAt": "2025-07-04T14:47:38.122Z", "updatedAt": "2025-07-04T14:47:38.122Z", "scannedAt": "2025-07-04T14:47:38.122Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/andersen/andersen.role.md", "fileType": "role"}}, {"id": "libai-poet", "source": "project", "protocol": "role", "name": "Libai Poet 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/libai-poet/libai-poet.role.md", "metadata": {"createdAt": "2025-07-04T14:47:38.122Z", "updatedAt": "2025-07-04T14:47:38.122Z", "scannedAt": "2025-07-04T14:47:38.122Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/libai-poet/libai-poet.role.md", "fileType": "role"}}, {"id": "primary-chinese-teacher", "source": "project", "protocol": "role", "name": "Primary Chinese Teacher 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/primary-chinese-teacher/primary-chinese-teacher.role.md", "metadata": {"createdAt": "2025-07-04T14:47:38.122Z", "updatedAt": "2025-07-04T14:47:38.122Z", "scannedAt": "2025-07-04T14:47:38.122Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/primary-chinese-teacher/primary-chinese-teacher.role.md", "fileType": "role"}}, {"id": "tieba-grumpy-bro", "source": "project", "protocol": "role", "name": "Tieba Grumpy Bro 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/tieba-grumpy-bro/tieba-grumpy-bro.role.md", "metadata": {"createdAt": "2025-07-04T14:47:38.122Z", "updatedAt": "2025-07-04T14:47:38.122Z", "scannedAt": "2025-07-04T14:47:38.122Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/tieba-grumpy-bro/tieba-grumpy-bro.role.md", "fileType": "role"}}, {"id": "xiaohongshu-grass-planter", "source": "project", "protocol": "role", "name": "Xiaohongshu Grass Planter 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/xiaohongshu-grass-planter/xiaohongshu-grass-planter.role.md", "metadata": {"createdAt": "2025-07-04T14:47:38.122Z", "updatedAt": "2025-07-04T14:47:38.122Z", "scannedAt": "2025-07-04T14:47:38.122Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/xiaohongshu-grass-planter/xiaohongshu-grass-planter.role.md", "fileType": "role"}}, {"id": "java-springboot-developer", "source": "project", "protocol": "role", "name": "Java Springboot Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/java-springboot-developer/java-springboot-developer.role.md", "metadata": {"createdAt": "2025-07-04T14:47:38.123Z", "updatedAt": "2025-07-04T14:47:38.123Z", "scannedAt": "2025-07-04T14:47:38.123Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/java-springboot-developer.role.md", "fileType": "role"}}, {"id": "fairy-tale-creation", "source": "project", "protocol": "thought", "name": "Fairy Tale Creation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/andersen/thought/fairy-tale-creation.thought.md", "metadata": {"createdAt": "2025-07-04T14:47:38.124Z", "updatedAt": "2025-07-04T14:47:38.124Z", "scannedAt": "2025-07-04T14:47:38.124Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/andersen/thought/fairy-tale-creation.thought.md", "fileType": "thought"}}, {"id": "libai-poet", "source": "project", "protocol": "thought", "name": "Libai Poet 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/libai-poet/thought/libai-poet.thought.md", "metadata": {"createdAt": "2025-07-04T14:47:38.124Z", "updatedAt": "2025-07-04T14:47:38.124Z", "scannedAt": "2025-07-04T14:47:38.124Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/libai-poet/thought/libai-poet.thought.md", "fileType": "thought"}}, {"id": "tieba-grumpy-mindset", "source": "project", "protocol": "thought", "name": "Tieba Grumpy Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/tieba-grumpy-bro/thought/tieba-grumpy-mindset.thought.md", "metadata": {"createdAt": "2025-07-04T14:47:38.124Z", "updatedAt": "2025-07-04T14:47:38.124Z", "scannedAt": "2025-07-04T14:47:38.124Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/tieba-grumpy-bro/thought/tieba-grumpy-mindset.thought.md", "fileType": "thought"}}, {"id": "xiaohongshu-marketing", "source": "project", "protocol": "thought", "name": "Xiaohongshu Marketing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/xiaohongshu-grass-planter/thought/xiaohongshu-marketing.thought.md", "metadata": {"createdAt": "2025-07-04T14:47:38.124Z", "updatedAt": "2025-07-04T14:47:38.124Z", "scannedAt": "2025-07-04T14:47:38.124Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/xiaohongshu-grass-planter/thought/xiaohongshu-marketing.thought.md", "fileType": "thought"}}, {"id": "java-developer", "source": "project", "protocol": "thought", "name": "Java Developer 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/java-springboot-developer/thought/java-developer.thought.md", "metadata": {"createdAt": "2025-07-04T14:47:38.124Z", "updatedAt": "2025-07-04T14:47:38.124Z", "scannedAt": "2025-07-04T14:47:38.124Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/thought/java-developer.thought.md", "fileType": "thought"}}, {"id": "springboot-architecture", "source": "project", "protocol": "thought", "name": "Springboot Architecture 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/java-springboot-developer/thought/springboot-architecture.thought.md", "metadata": {"createdAt": "2025-07-04T14:47:38.125Z", "updatedAt": "2025-07-04T14:47:38.125Z", "scannedAt": "2025-07-04T14:47:38.125Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/thought/springboot-architecture.thought.md", "fileType": "thought"}}, {"id": "fairy-tale-structure", "source": "project", "protocol": "execution", "name": "Fairy Tale Structure 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/andersen/execution/fairy-tale-structure.execution.md", "metadata": {"createdAt": "2025-07-04T14:47:38.126Z", "updatedAt": "2025-07-04T14:47:38.126Z", "scannedAt": "2025-07-04T14:47:38.126Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/andersen/execution/fairy-tale-structure.execution.md", "fileType": "execution"}}, {"id": "storytelling-craft", "source": "project", "protocol": "execution", "name": "Storytelling Craft 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/andersen/execution/storytelling-craft.execution.md", "metadata": {"createdAt": "2025-07-04T14:47:38.126Z", "updatedAt": "2025-07-04T14:47:38.126Z", "scannedAt": "2025-07-04T14:47:38.126Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/andersen/execution/storytelling-craft.execution.md", "fileType": "execution"}}, {"id": "poetry-creation", "source": "project", "protocol": "execution", "name": "Poetry Creation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/libai-poet/execution/poetry-creation.execution.md", "metadata": {"createdAt": "2025-07-04T14:47:38.126Z", "updatedAt": "2025-07-04T14:47:38.126Z", "scannedAt": "2025-07-04T14:47:38.126Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/libai-poet/execution/poetry-creation.execution.md", "fileType": "execution"}}, {"id": "grumpy-bro-behavior", "source": "project", "protocol": "execution", "name": "Grumpy Bro Behavior 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/tieba-grumpy-bro/execution/grumpy-bro-behavior.execution.md", "metadata": {"createdAt": "2025-07-04T14:47:38.127Z", "updatedAt": "2025-07-04T14:47:38.127Z", "scannedAt": "2025-07-04T14:47:38.127Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/tieba-grumpy-bro/execution/grumpy-bro-behavior.execution.md", "fileType": "execution"}}, {"id": "grass-planting-workflow", "source": "project", "protocol": "execution", "name": "Grass Planting Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/xiaohongshu-grass-planter/execution/grass-planting-workflow.execution.md", "metadata": {"createdAt": "2025-07-04T14:47:38.127Z", "updatedAt": "2025-07-04T14:47:38.127Z", "scannedAt": "2025-07-04T14:47:38.127Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/xiaohongshu-grass-planter/execution/grass-planting-workflow.execution.md", "fileType": "execution"}}, {"id": "java-springboot-development-workflow", "source": "project", "protocol": "execution", "name": "Java Springboot Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/java-springboot-developer/execution/java-springboot-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-04T14:47:38.127Z", "updatedAt": "2025-07-04T14:47:38.127Z", "scannedAt": "2025-07-04T14:47:38.127Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/execution/java-springboot-development-workflow.execution.md", "fileType": "execution"}}, {"id": "quality-standards", "source": "project", "protocol": "execution", "name": "Quality Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/java-springboot-developer/execution/quality-standards.execution.md", "metadata": {"createdAt": "2025-07-04T14:47:38.127Z", "updatedAt": "2025-07-04T14:47:38.127Z", "scannedAt": "2025-07-04T14:47:38.127Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/execution/quality-standards.execution.md", "fileType": "execution"}}, {"id": "child-psychology", "source": "project", "protocol": "knowledge", "name": "Child Psychology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/andersen/knowledge/child-psychology.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.128Z", "updatedAt": "2025-07-04T14:47:38.128Z", "scannedAt": "2025-07-04T14:47:38.128Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/andersen/knowledge/child-psychology.knowledge.md", "fileType": "knowledge"}}, {"id": "fairy-tale-traditions", "source": "project", "protocol": "knowledge", "name": "Fairy Tale Traditions 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/andersen/knowledge/fairy-tale-traditions.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.128Z", "updatedAt": "2025-07-04T14:47:38.128Z", "scannedAt": "2025-07-04T14:47:38.128Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/andersen/knowledge/fairy-tale-traditions.knowledge.md", "fileType": "knowledge"}}, {"id": "narrative-techniques", "source": "project", "protocol": "knowledge", "name": "Narrative Techniques 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/andersen/knowledge/narrative-techniques.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.129Z", "updatedAt": "2025-07-04T14:47:38.129Z", "scannedAt": "2025-07-04T14:47:38.129Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/andersen/knowledge/narrative-techniques.knowledge.md", "fileType": "knowledge"}}, {"id": "classical-poetry", "source": "project", "protocol": "knowledge", "name": "Classical Poetry 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/libai-poet/knowledge/classical-poetry.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.129Z", "updatedAt": "2025-07-04T14:47:38.129Z", "scannedAt": "2025-07-04T14:47:38.129Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/libai-poet/knowledge/classical-poetry.knowledge.md", "fileType": "knowledge"}}, {"id": "tieba-culture", "source": "project", "protocol": "knowledge", "name": "Tieba Culture 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/tieba-grumpy-bro/knowledge/tieba-culture.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.129Z", "updatedAt": "2025-07-04T14:47:38.129Z", "scannedAt": "2025-07-04T14:47:38.129Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/tieba-grumpy-bro/knowledge/tieba-culture.knowledge.md", "fileType": "knowledge"}}, {"id": "consumer-psychology", "source": "project", "protocol": "knowledge", "name": "Consumer Psychology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/xiaohongshu-grass-planter/knowledge/consumer-psychology.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.129Z", "updatedAt": "2025-07-04T14:47:38.129Z", "scannedAt": "2025-07-04T14:47:38.129Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/xiaohongshu-grass-planter/knowledge/consumer-psychology.knowledge.md", "fileType": "knowledge"}}, {"id": "xiaohongshu-ecosystem", "source": "project", "protocol": "knowledge", "name": "Xiaohongshu Ecosystem 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/xiaohongshu-grass-planter/knowledge/xiaohongshu-ecosystem.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.130Z", "updatedAt": "2025-07-04T14:47:38.130Z", "scannedAt": "2025-07-04T14:47:38.130Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/domain/xiaohongshu-grass-planter/knowledge/xiaohongshu-ecosystem.knowledge.md", "fileType": "knowledge"}}, {"id": "database-technologies", "source": "project", "protocol": "knowledge", "name": "Database Technologies 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/java-springboot-developer/knowledge/database-technologies.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.130Z", "updatedAt": "2025-07-04T14:47:38.130Z", "scannedAt": "2025-07-04T14:47:38.130Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/knowledge/database-technologies.knowledge.md", "fileType": "knowledge"}}, {"id": "devops-tools", "source": "project", "protocol": "knowledge", "name": "Devops Tools 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/java-springboot-developer/knowledge/devops-tools.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.130Z", "updatedAt": "2025-07-04T14:47:38.130Z", "scannedAt": "2025-07-04T14:47:38.130Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/knowledge/devops-tools.knowledge.md", "fileType": "knowledge"}}, {"id": "java-core", "source": "project", "protocol": "knowledge", "name": "Java Core 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/java-springboot-developer/knowledge/java-core.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.130Z", "updatedAt": "2025-07-04T14:47:38.130Z", "scannedAt": "2025-07-04T14:47:38.130Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/knowledge/java-core.knowledge.md", "fileType": "knowledge"}}, {"id": "microservices-architecture", "source": "project", "protocol": "knowledge", "name": "Microservices Architecture 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/java-springboot-developer/knowledge/microservices-architecture.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.131Z", "updatedAt": "2025-07-04T14:47:38.131Z", "scannedAt": "2025-07-04T14:47:38.131Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/knowledge/microservices-architecture.knowledge.md", "fileType": "knowledge"}}, {"id": "springboot-framework", "source": "project", "protocol": "knowledge", "name": "Springboot Framework 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/java-springboot-developer/knowledge/springboot-framework.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.131Z", "updatedAt": "2025-07-04T14:47:38.131Z", "scannedAt": "2025-07-04T14:47:38.131Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/knowledge/springboot-framework.knowledge.md", "fileType": "knowledge"}}, {"id": "testing-frameworks", "source": "project", "protocol": "knowledge", "name": "Testing Frameworks 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/java-springboot-developer/knowledge/testing-frameworks.knowledge.md", "metadata": {"createdAt": "2025-07-04T14:47:38.131Z", "updatedAt": "2025-07-04T14:47:38.131Z", "scannedAt": "2025-07-04T14:47:38.131Z", "filePath": "/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/.promptx/resource/role/java-springboot-developer/knowledge/testing-frameworks.knowledge.md", "fileType": "knowledge"}}], "stats": {"totalResources": 32, "byProtocol": {"role": 6, "thought": 6, "execution": 7, "knowledge": 13}, "bySource": {"project": 32}}}