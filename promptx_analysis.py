#!/usr/bin/env python3
"""
PromptX GitHub项目数据科学分析
分析项目的各项指标并生成见解
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class PromptXAnalyzer:
    def __init__(self):
        """初始化PromptX项目分析器"""
        self.project_data = {
            'name': 'PromptX',
            'stars': 991,
            'forks': 79,
            'watchers': 8,
            'contributors': 8,
            'commits': 193,
            'language': 'JavaScript',
            'language_percentage': 97.8,
            'releases': 0,
            'license': 'MIT',
            'creation_date': '2024-12-01',  # 估算创建日期
            'last_update': '2025-01-23',
            'primary_features': [
                'AI专业能力增强',
                '角色管理系统', 
                '记忆管理',
                '知识系统集成',
                'MCP协议支持'
            ]
        }
        
        # 行业对标数据
        self.benchmark_data = {
            'similar_projects': [
                {'name': 'LangChain', 'stars': 95000, 'age_months': 24},
                {'name': 'AutoGPT', 'stars': 167000, 'age_months': 12},
                {'name': 'OpenAI-Cookbook', 'stars': 59000, 'age_months': 18},
                {'name': 'Semantic-Kernel', 'stars': 21000, 'age_months': 15}
            ]
        }
        
    def calculate_growth_metrics(self):
        """计算增长指标"""
        # 估算项目存在时间（月）
        project_age_months = 2  # 约2个月
        
        metrics = {
            'stars_per_month': self.project_data['stars'] / project_age_months,
            'forks_per_month': self.project_data['forks'] / project_age_months,
            'commits_per_month': self.project_data['commits'] / project_age_months,
            'star_to_fork_ratio': self.project_data['stars'] / self.project_data['forks'],
            'contributor_engagement': self.project_data['commits'] / self.project_data['contributors'],
            'project_age_months': project_age_months
        }
        
        return metrics
    
    def analyze_project_health(self):
        """分析项目健康度"""
        metrics = self.calculate_growth_metrics()
        
        health_score = 0
        factors = {}
        
        # 增长速度评分 (0-25分)
        stars_pm = metrics['stars_per_month']
        if stars_pm > 400:
            growth_score = 25
        elif stars_pm > 200:
            growth_score = 20
        elif stars_pm > 100:
            growth_score = 15
        else:
            growth_score = 10
        factors['growth_speed'] = growth_score
        
        # 社区参与度评分 (0-25分)
        fork_ratio = metrics['star_to_fork_ratio']
        if 5 <= fork_ratio <= 15:
            community_score = 25
        elif 3 <= fork_ratio <= 20:
            community_score = 20
        else:
            community_score = 15
        factors['community_engagement'] = community_score
        
        # 开发活跃度评分 (0-25分)
        commits_pm = metrics['commits_per_month']
        if commits_pm > 80:
            activity_score = 25
        elif commits_pm > 50:
            activity_score = 20
        elif commits_pm > 30:
            activity_score = 15
        else:
            activity_score = 10
        factors['development_activity'] = activity_score
        
        # 技术成熟度评分 (0-25分)
        maturity_score = 15  # 基于无正式发布版本但代码结构完整
        if self.project_data['releases'] > 0:
            maturity_score += 10
        factors['technical_maturity'] = maturity_score
        
        health_score = sum(factors.values())
        
        return {
            'total_score': health_score,
            'factors': factors,
            'grade': self.get_health_grade(health_score)
        }
    
    def get_health_grade(self, score):
        """根据分数获取健康等级"""
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B+'
        elif score >= 60:
            return 'B'
        else:
            return 'C'
    
    def benchmark_analysis(self):
        """对标分析"""
        benchmarks = []
        for project in self.benchmark_data['similar_projects']:
            stars_per_month = project['stars'] / project['age_months']
            benchmarks.append({
                'name': project['name'],
                'stars': project['stars'],
                'stars_per_month': stars_per_month,
                'age_months': project['age_months']
            })
        
        promptx_metrics = self.calculate_growth_metrics()
        
        # 添加PromptX数据
        benchmarks.append({
            'name': 'PromptX',
            'stars': self.project_data['stars'],
            'stars_per_month': promptx_metrics['stars_per_month'],
            'age_months': promptx_metrics['project_age_months']
        })
        
        return pd.DataFrame(benchmarks)
    
    def generate_insights(self):
        """生成数据洞察"""
        metrics = self.calculate_growth_metrics()
        health = self.analyze_project_health()
        
        insights = {
            'strengths': [
                f"每月获得{metrics['stars_per_month']:.0f}个stars，增长速度优秀",
                f"Star-Fork比例为{metrics['star_to_fork_ratio']:.1f}，社区参与度健康", 
                f"每个贡献者平均{metrics['contributor_engagement']:.1f}次提交，开发效率高",
                "JavaScript技术栈主流，易于社区贡献",
                "MIT开源协议，商业友好"
            ],
            'opportunities': [
                "尚未发布正式版本，可通过release提升专业度",
                "Watch数量相对较少，可加强项目推广",
                "可扩展更多编程语言支持",
                "建立更完善的文档和示例",
                "增加CI/CD自动化流程"
            ],
            'risks': [
                "项目年龄较短，需要证明长期维护能力",
                "竞争对手众多，需要明确差异化优势",
                "技术依赖性较强，需要跟上AI发展趋势"
            ],
            'recommendations': [
                "立即发布v1.0正式版本",
                "制定3-6个月的功能路线图",
                "开展社区营销和技术分享",
                "建立用户反馈收集机制",
                "寻找关键意见领袖(KOL)背书"
            ]
        }
        
        return insights, health
    
    def create_visualization(self):
        """创建数据可视化"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 项目基础指标
        metrics = ['Stars', 'Forks', 'Contributors', 'Commits']
        values = [991, 79, 8, 193]
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4']
        
        bars = ax1.bar(metrics, values, color=colors)
        ax1.set_title('PromptX 项目基础指标', fontsize=14, fontweight='bold')
        ax1.set_ylabel('数量')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom')
        
        # 2. 对标分析
        benchmark_df = self.benchmark_analysis()
        ax2.scatter(benchmark_df['age_months'], benchmark_df['stars_per_month'], 
                   s=100, alpha=0.7)
        
        for i, row in benchmark_df.iterrows():
            ax2.annotate(row['name'], 
                        (row['age_months'], row['stars_per_month']),
                        xytext=(5, 5), textcoords='offset points')
        
        ax2.set_xlabel('项目年龄 (月)')
        ax2.set_ylabel('每月Stars增长')
        ax2.set_title('PromptX vs 竞品增长对比', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 3. 健康度分析
        health = self.analyze_project_health()
        factors = list(health['factors'].keys())
        scores = list(health['factors'].values())
        factor_names = ['增长速度', '社区参与', '开发活跃', '技术成熟']
        
        ax3.pie(scores, labels=factor_names, autopct='%1.1f%%', startangle=90)
        ax3.set_title(f'项目健康度分析 (总分: {health["total_score"]}/100, 等级: {health["grade"]})', 
                     fontsize=14, fontweight='bold')
        
        # 4. 增长趋势预测
        months = np.array([1, 2, 3, 4, 5, 6])
        current_growth = self.calculate_growth_metrics()['stars_per_month']
        
        # 三种增长场景
        conservative = current_growth * 0.8 * months + 991
        moderate = current_growth * months + 991  
        optimistic = current_growth * 1.5 * months + 991
        
        ax4.plot(months, conservative, '--', label='保守预测', alpha=0.7)
        ax4.plot(months, moderate, '-', label='中等预测', linewidth=2)
        ax4.plot(months, optimistic, '-.', label='乐观预测', alpha=0.7)
        ax4.axhline(y=991, color='red', linestyle=':', label='当前Stars')
        
        ax4.set_xlabel('未来月份')
        ax4.set_ylabel('预测Stars数量')
        ax4.set_title('PromptX Stars增长预测', fontsize=14, fontweight='bold')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/promptx_analysis.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig
    
    def generate_full_report(self):
        """生成完整分析报告"""
        print("=" * 60)
        print("🚀 PromptX GitHub项目数据科学分析报告")
        print("=" * 60)
        
        # 基础数据
        print("\n📊 项目基础数据:")
        print(f"⭐ Stars: {self.project_data['stars']:,}")
        print(f"🍴 Forks: {self.project_data['forks']:,}")
        print(f"👀 Watchers: {self.project_data['watchers']:,}")
        print(f"👥 Contributors: {self.project_data['contributors']:,}")
        print(f"💻 Commits: {self.project_data['commits']:,}")
        print(f"📝 Primary Language: {self.project_data['language']} ({self.project_data['language_percentage']}%)")
        
        # 增长指标
        metrics = self.calculate_growth_metrics()
        print(f"\n📈 增长指标分析:")
        print(f"每月Stars增长: {metrics['stars_per_month']:.0f}")
        print(f"每月Forks增长: {metrics['forks_per_month']:.0f}")
        print(f"每月Commits: {metrics['commits_per_month']:.0f}")
        print(f"Star-Fork比例: {metrics['star_to_fork_ratio']:.1f}")
        print(f"人均提交数: {metrics['contributor_engagement']:.1f}")
        
        # 健康度分析
        insights, health = self.generate_insights()
        print(f"\n🏥 项目健康度评估:")
        print(f"总体得分: {health['total_score']}/100")
        print(f"健康等级: {health['grade']}")
        print("各维度得分:")
        for factor, score in health['factors'].items():
            factor_name = {'growth_speed': '增长速度', 'community_engagement': '社区参与', 
                          'development_activity': '开发活跃', 'technical_maturity': '技术成熟'}[factor]
            print(f"  {factor_name}: {score}/25")
        
        # 对标分析
        print(f"\n🎯 竞品对标分析:")
        benchmark_df = self.benchmark_analysis()
        print(benchmark_df.to_string(index=False))
        
        # 关键洞察
        print(f"\n💡 关键洞察:")
        print("优势:")
        for strength in insights['strengths']:
            print(f"  ✅ {strength}")
        
        print("\n机会:")
        for opportunity in insights['opportunities']:
            print(f"  🎯 {opportunity}")
            
        print("\n风险:")
        for risk in insights['risks']:
            print(f"  ⚠️ {risk}")
            
        print("\n建议:")
        for rec in insights['recommendations']:
            print(f"  📋 {rec}")
        
        print("\n" + "=" * 60)
        print("📊 可视化图表已保存至: promptx_analysis.png")
        print("=" * 60)
        
        return {
            'metrics': metrics,
            'health': health,
            'insights': insights,
            'benchmark': benchmark_df
        }

# 执行分析
if __name__ == "__main__":
    analyzer = PromptXAnalyzer()
    report = analyzer.generate_full_report()
    analyzer.create_visualization()
    
    # 保存详细数据
    import json
    with open('/Users/<USER>/WorkSpaces/DeepracticeProjects/PromptXDemo/promptx_analysis_data.json', 'w', encoding='utf-8') as f:
        # 转换DataFrame为字典以便JSON序列化
        report_data = report.copy()
        report_data['benchmark'] = report_data['benchmark'].to_dict('records')
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 分析完成！详细数据已保存至 promptx_analysis_data.json")